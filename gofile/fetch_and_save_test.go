package gofile

import (
	"bytes"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestProcess sets up the test environment
func setupTestProcess(t *testing.T) {
	// Simple test setup without external dependencies
	// Create test directories if needed
	testDir := filepath.Join(os.TempDir(), "gofile_test")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Clean up function
	t.Cleanup(func() {
		if err := os.RemoveAll(testDir); err != nil {
			t.Fatalf("Failed to remove test directory: %v", err)
		}
	})
}

// Helper functions
func createTestImage(width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	blue := color.RGBA{B: 255, A: 255}
	draw.Draw(img, img.Bounds(), &image.Uniform{blue}, image.Point{}, draw.Src)
	return img
}

// createLargeTestImage creates a large test image with gradient effect to increase file size
func createLargeTestImage(width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// Create gradient effect to increase file size
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			r := uint8((x * 255) / width)
			g := uint8((y * 255) / height)
			b := uint8(((x + y) * 255) / (width + height))
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return img
}

func setupTestServer(t *testing.T, handler http.HandlerFunc) *httptest.Server {
	server := httptest.NewServer(handler)
	t.Cleanup(func() {
		server.Close()
	})
	return server
}

func createTempDir(t *testing.T) string {
	tmpDir, err := os.MkdirTemp("", "test_*")
	require.NoError(t, err)
	t.Cleanup(func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Fatalf("Failed to remove temp directory: %v", err)
		}
	})
	return tmpDir
}

// Basic functionality tests
func TestSaveAsJPEG(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
	}{
		{
			name:    "Valid image and path",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Nil image",
			img:     nil,
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: true,
		},
		{
			name:    "Invalid path",
			img:     createTestImage(100, 100),
			path:    "/invalid/path/test.jpg",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := saveAsJPEG(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify file is valid JPEG
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()
			_, err = jpeg.Decode(file)
			assert.NoError(t, err)
		})
	}
}

func TestSaveAsWebP(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
	}{
		{
			name:    "Valid image and path",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.webp"),
			wantErr: false,
		},
		{
			name:    "Nil image",
			img:     nil,
			path:    filepath.Join(createTempDir(t), "test.webp"),
			wantErr: true,
		},
		{
			name:    "Invalid path",
			img:     createTestImage(100, 100),
			path:    "/invalid/path/test.webp",
			wantErr: true,
		},
		{
			name:    "Invalid extension",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.txt"),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := saveAsWebP(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify WebP file
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()
			header := make([]byte, 12)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, "RIFF", string(header[0:4]))
			assert.Equal(t, "WEBP", string(header[8:12]))
		})
	}
}

// Additional saveAsJPEG tests for better coverage
func TestSaveAsJPEG_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Very small image",
			img:     createTestImage(1, 1),
			path:    filepath.Join(createTempDir(t), "tiny.jpg"),
			wantErr: false,
		},
		{
			name:    "Large image",
			img:     createTestImage(1000, 1000),
			path:    filepath.Join(createTempDir(t), "large.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with nested directories",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "nested", "deep", "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with special characters",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test-file_123.jpg"),
			wantErr: false,
		},
		{
			name:    "Empty filename",
			img:     createTestImage(50, 50),
			path:    createTempDir(t) + "/",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t, tt.path)
			}

			path, err := saveAsJPEG(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.NotEmpty(t, path)
			assert.FileExists(t, path)

			// Verify it's a JPEG file
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Logf("Failed to close file: %v", err)
				}
			}()
			header := make([]byte, 3)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, []byte{0xFF, 0xD8, 0xFF}, header) // JPEG magic bytes
		})
	}
}

// Additional saveAsWebP tests for better coverage
func TestSaveAsWebP_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Empty path",
			img:     createTestImage(50, 50),
			path:    "",
			wantErr: false, // Empty path gets converted to ".webp"
		},
		{
			name:    "Path with no extension",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test"),
			wantErr: false,
		},
		{
			name:    "Path with .jpg extension (should convert to .webp)",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with .png extension (should convert to .webp)",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test.png"),
			wantErr: false,
		},
		{
			name:    "Very small image",
			img:     createTestImage(1, 1),
			path:    filepath.Join(createTempDir(t), "tiny.webp"),
			wantErr: false,
		},
		{
			name:    "Large image",
			img:     createTestImage(500, 500),
			path:    filepath.Join(createTempDir(t), "large.webp"),
			wantErr: false,
		},
		{
			name:    "Read-only directory",
			img:     createTestImage(50, 50),
			path:    "/tmp/readonly/test.webp",
			wantErr: true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0444); err != nil {
					t.Fatalf("Failed to create read-only directory: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t, tt.path)
			}

			path, err := saveAsWebP(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.NotEmpty(t, path)
			assert.FileExists(t, path)

			// Verify it's a WebP file
			if filepath.Ext(path) == ".webp" {
				file, err := os.Open(path)
				require.NoError(t, err)
				defer func() {
					if err := file.Close(); err != nil {
						t.Logf("Failed to close file: %v", err)
					}
				}()
				header := make([]byte, 12)
				_, err = io.ReadFull(file, header)
				require.NoError(t, err)
				assert.Equal(t, "RIFF", string(header[0:4]))
				assert.Equal(t, "WEBP", string(header[8:12]))
			}
		})
	}
}

func TestSaveImage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		path         string
		compressWebP bool
		wantErr      bool
		setup        func(t *testing.T, path string)
	}{
		{
			name:         "Save as JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Save as WebP",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Nil image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Invalid path",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "invalid", "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup: func(t *testing.T, path string) {
				// Create a read-only directory
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
		{
			name:         "WebP fallback to JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)

			path, err := SaveImage(tt.img, tt.path, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify file format
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()

			if tt.compressWebP {
				header := make([]byte, 12)
				_, err = io.ReadFull(file, header)
				require.NoError(t, err)
				assert.Equal(t, "RIFF", string(header[0:4]))
				assert.Equal(t, "WEBP", string(header[8:12]))
			} else {
				_, err = jpeg.Decode(file)
				assert.NoError(t, err)
			}
		})
	}
}

// Error handling tests
func TestDownloadWithRetry(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(attempts *int) (int, []byte)
		maxRetries     int
		wantErr        bool
		expectedTries  int
	}{
		{
			name: "Successful first try",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusOK, []byte("test")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 1,
		},
		{
			name: "Success after two failures",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				if *attempts <= 2 {
					return http.StatusInternalServerError, []byte("error")
				}
				return http.StatusOK, []byte("test")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 3,
		},
		{
			name: "All attempts fail",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "Timeout error",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				time.Sleep(2 * time.Second)
				return http.StatusOK, []byte("test")
			},
			maxRetries:    1,
			wantErr:       true,
			expectedTries: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attempts := 0
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				status, data := tt.serverBehavior(&attempts)
				w.WriteHeader(status)
				if _, err := w.Write(data); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			}))

			// Set a shorter timeout for the test
			client := &http.Client{
				Timeout: 1 * time.Second,
				Transport: &http.Transport{
					ResponseHeaderTimeout: 1 * time.Second,
					ExpectContinueTimeout: 1 * time.Second,
					IdleConnTimeout:       1 * time.Second,
					TLSHandshakeTimeout:   1 * time.Second,
					DialContext: (&net.Dialer{
						Timeout: 1 * time.Second,
					}).DialContext,
					DisableKeepAlives:   true,
					MaxIdleConns:        1,
					MaxConnsPerHost:     1,
					MaxIdleConnsPerHost: 1,
					DisableCompression:  true,
					ForceAttemptHTTP2:   false,
				},
			}
			// Use our test client override mechanism
			SetTestHTTPClient(client)
			defer func() {
				SetTestHTTPClient(nil) // Reset to default
			}()

			resp, err := downloadWithRetry(server.URL, tt.maxRetries)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, http.StatusOK, resp.StatusCode)
			}
			assert.Equal(t, tt.expectedTries, attempts)
		})
	}
}

// Additional downloadWithRetry tests for better coverage
func TestDownloadWithRetry_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(attempts *int) (int, []byte)
		maxRetries     int
		wantErr        bool
		expectedTries  int
	}{
		{
			name: "Zero retries",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    0,
			wantErr:       true,
			expectedTries: 0, // With 0 retries, no attempts should be made
		},
		{
			name: "Negative retries",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    -1,
			wantErr:       true,
			expectedTries: 0, // With negative retries, no attempts should be made
		},
		{
			name: "404 Not Found",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusNotFound, []byte("not found")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "403 Forbidden",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusForbidden, []byte("forbidden")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "Success on last retry",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				if *attempts < 3 {
					return http.StatusBadGateway, []byte("error")
				}
				return http.StatusOK, []byte("success")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attempts := 0
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				status, data := tt.serverBehavior(&attempts)
				w.WriteHeader(status)
				if _, err := w.Write(data); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			}))

			resp, err := downloadWithRetry(server.URL, tt.maxRetries)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, http.StatusOK, resp.StatusCode)
			}
			assert.Equal(t, tt.expectedTries, attempts)
		})
	}
}

// Edge cases and special scenarios
func TestDownloadAndSaveImageInDirs(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		savePaths      []string
		compressWebP   bool
		wantErr        bool
		checkResult    func(t *testing.T, results map[string]string)
	}{
		{
			name: "Successful save to multiple paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths: []string{
				filepath.Join(createTempDir(t), "test1.jpg"),
				filepath.Join(createTempDir(t), "test2.jpg"),
			},
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
		{
			name: "WebP compression with fallback",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
			compressWebP: true,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					file, err := os.Open(path)
					require.NoError(t, err)
					defer func() {
						if err := file.Close(); err != nil {
							t.Fatalf("Failed to close file: %v", err)
						}
					}()
					header := make([]byte, 12)
					_, err = io.ReadFull(file, header)
					require.NoError(t, err)
					assert.Equal(t, "RIFF", string(header[0:4]))
					assert.Equal(t, "WEBP", string(header[8:12]))
				}
			},
		},
		{
			name: "Invalid image data",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			savePaths:    []string{filepath.Join(createTempDir(t), "test.jpg")},
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Partial success",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths: []string{
				filepath.Join(createTempDir(t), "valid.jpg"),
				"/invalid/path/test.jpg",
			},
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			results, err := DownloadAndSaveImageInDirs(server.URL, tt.savePaths, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
				return
			}
			assert.NoError(t, err)
			tt.checkResult(t, results)
		})
	}
}

func TestDownloadAndResizeImage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		width          int
		height         int
		wantErr        bool
		checkSize      func(t *testing.T, img image.Image)
	}{
		{
			name: "Successful resize landscape",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(200, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 100, bounds.Dx())
				assert.Equal(t, 50, bounds.Dy())
			},
		},
		{
			name: "Successful resize portrait",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 200)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 50, bounds.Dx())
				assert.Equal(t, 100, bounds.Dy())
			},
		},
		{
			name: "Invalid image data",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: true,
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			width:   100,
			height:  100,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			img, err := DownloadAndResizeImage(server.URL, tt.width, tt.height)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, img)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, img)
			tt.checkSize(t, img)
		})
	}
}

// Add more test cases to improve coverage
func TestDownloadAndSaveImageInDirs_WithMultipleFormats(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	savePaths := []string{
		filepath.Join(createTempDir(t), "test1.webp"),
		filepath.Join(createTempDir(t), "test2.jpg"),
	}

	results, err := DownloadAndSaveImageInDirs(server.URL, savePaths, true)
	assert.NoError(t, err)
	assert.Len(t, results, 2)

	for _, path := range results {
		assert.FileExists(t, path)
		file, err := os.Open(path)
		require.NoError(t, err)
		defer func() {
			if err := file.Close(); err != nil {
				t.Fatalf("Failed to close file: %v", err)
			}
		}()

		if filepath.Ext(path) == ".webp" {
			header := make([]byte, 12)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, "RIFF", string(header[0:4]))
			assert.Equal(t, "WEBP", string(header[8:12]))
		} else {
			_, err = jpeg.Decode(file)
			assert.NoError(t, err)
		}
	}
}

// Smart Compression Tests

func TestSmartCompression(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name          string
		imageWidth    int
		imageHeight   int
		targetSizeKB  int
		compressWebP  bool
		expectSuccess bool
	}{
		{
			name:          "Large image to 300KB JPEG",
			imageWidth:    1200,
			imageHeight:   800,
			targetSizeKB:  300,
			compressWebP:  false,
			expectSuccess: true,
		},
		{
			name:          "Large image to 100KB JPEG",
			imageWidth:    1000,
			imageHeight:   1000,
			targetSizeKB:  100,
			compressWebP:  false,
			expectSuccess: true,
		},
		{
			name:          "Large image to 300KB WebP",
			imageWidth:    1200,
			imageHeight:   800,
			targetSizeKB:  300,
			compressWebP:  true,
			expectSuccess: true,
		},
		{
			name:          "Small image within target size",
			imageWidth:    800,
			imageHeight:   600,
			targetSizeKB:  100, // Changed from 10 to 100 to match new logic
			compressWebP:  false,
			expectSuccess: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)

				// Create large image
				img := createLargeTestImage(tt.imageWidth, tt.imageHeight)
				err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95})
				require.NoError(t, err)
			}))
			defer server.Close()

			// Create temporary directory
			tmpDir := createTempDir(t)
			var savePath string
			if tt.compressWebP {
				savePath = filepath.Join(tmpDir, "compressed.webp")
			} else {
				savePath = filepath.Join(tmpDir, "compressed.jpg")
			}

			// Test smart compression
			savedPath, err := DownloadAndSaveImageWithSmartCompression(
				server.URL,
				savePath,
				tt.targetSizeKB,
				tt.compressWebP,
				1, // maxRetries
			)

			if tt.expectSuccess {
				assert.NoError(t, err)
				assert.NotEmpty(t, savedPath)
				assert.FileExists(t, savedPath)

				// Check file size
				fileInfo, err := os.Stat(savedPath)
				require.NoError(t, err)

				fileSizeKB := fileInfo.Size() / 1024
				t.Logf("Target: %dKB, Actual: %dKB", tt.targetSizeKB, fileSizeKB)

				// New logic: if image is within target size, return as-is
				// If image is larger than target, resize to max dimension (1280px)
				if tt.targetSizeKB >= 100 { // For reasonable target sizes
					// Allow some flexibility for compression results
					maxAllowedSizeKB := int64(tt.targetSizeKB) * 150 / 100
					assert.LessOrEqual(t, fileSizeKB, maxAllowedSizeKB,
						"File size %dKB should be close to target %dKB", fileSizeKB, tt.targetSizeKB)
				} else {
					// For very small targets, just ensure the file exists and is reasonable
					assert.Greater(t, fileSizeKB, int64(0), "File should have some content")
				}
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestDownloadAndSaveFileWithSmartCompression(t *testing.T) {
	setupTestProcess(t)

	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)

		// Create large image
		img := createLargeTestImage(1000, 800)
		err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95})
		require.NoError(t, err)
	}))
	defer server.Close()

	tmpDir := createTempDir(t)
	savePath := filepath.Join(tmpDir, "smart_compressed.jpg")

	// Test smart compression through DownloadAndSaveFile
	opts := &DownloadAndSaveFileOptions{
		URL:            server.URL,
		SavePaths:      []string{savePath},
		IsPhoto:        true,
		CompressWebP:   false,
		CompressToSize: true,
		TargetSizeKB:   200,
		MaxRetries:     1,
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[0])

	// Check file size
	fileInfo, err := os.Stat(results[0])
	require.NoError(t, err)

	fileSizeKB := fileInfo.Size() / 1024
	t.Logf("Target: 200KB, Actual: %dKB", fileSizeKB)

	// File size should be close to target size
	assert.LessOrEqual(t, fileSizeKB, int64(300), // Allow 150% margin
		"File size %dKB should be close to target 200KB", fileSizeKB)
}

func TestResizeImageToMaxDimension(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name        string
		imageWidth  int
		imageHeight int
		isWebP      bool
	}{
		{
			name:        "JPEG resize",
			imageWidth:  1600,
			imageHeight: 1200,
			isWebP:      false,
		},
		{
			name:        "WebP resize",
			imageWidth:  1600,
			imageHeight: 1200,
			isWebP:      true,
		},
		{
			name:        "Small image no resize needed",
			imageWidth:  800,
			imageHeight: 600,
			isWebP:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test image
			img := createLargeTestImage(tt.imageWidth, tt.imageHeight)

			// Test resize function
			resizedImg, finalSizeKB, err := ResizeImageToMaxDimension(img, tt.isWebP)

			assert.NoError(t, err)
			assert.NotNil(t, resizedImg)
			assert.Greater(t, finalSizeKB, 0)

			// Check resized dimensions
			bounds := resizedImg.Bounds()
			resizedWidth := bounds.Dx()
			resizedHeight := bounds.Dy()

			t.Logf("Original: %dx%d, Resized: %dx%d, Final size: %dKB",
				tt.imageWidth, tt.imageHeight, resizedWidth, resizedHeight, finalSizeKB)

			// Check that larger dimension is not greater than MAX_DIMENSION_PIXELS
			maxDim := resizedWidth
			if resizedHeight > maxDim {
				maxDim = resizedHeight
			}
			assert.LessOrEqual(t, maxDim, MAX_DIMENSION_PIXELS,
				"Larger dimension should not exceed MAX_DIMENSION_PIXELS")

			// If original was larger than MAX_DIMENSION_PIXELS, check that it was resized
			originalMaxDim := tt.imageWidth
			if tt.imageHeight > originalMaxDim {
				originalMaxDim = tt.imageHeight
			}
			if originalMaxDim > MAX_DIMENSION_PIXELS {
				assert.True(t, resizedWidth < tt.imageWidth || resizedHeight < tt.imageHeight,
					"Large image should be resized")
			}
		})
	}
}

func TestDownloadAndSaveImageInDirsWithCompression_FastPath(t *testing.T) {
	setupTestProcess(t)

	// Create a small JPEG image that should trigger the fast path
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)

		// Create a small image (should be well under 300KB)
		img := createTestImage(200, 200)
		err := jpeg.Encode(w, img, &jpeg.Options{Quality: 80})
		require.NoError(t, err)
	}))
	defer server.Close()

	tmpDir := createTempDir(t)
	savePath := filepath.Join(tmpDir, "fast_path_test.jpg")

	// Use URL with .jpg extension to ensure no format conversion is needed
	testURL := server.URL + "/test.jpg"

	// Test conditions for fast path:
	// - compressWebP = false
	// - compressToSize = false
	// - image size < 300KB
	// - no format conversion needed (JPEG -> JPEG)
	results, err := DownloadAndSaveImageInDirsWithCompression(
		testURL,
		[]string{savePath},
		false, // compressWebP = false
		false, // compressToSize = false
		300,   // targetSizeKB
		1,     // maxRetries
	)

	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[savePath])

	// Verify the file was saved
	fileInfo, err := os.Stat(results[savePath])
	require.NoError(t, err)
	fileSizeKB := fileInfo.Size() / 1024

	t.Logf("Fast path test - File size: %dKB", fileSizeKB)
	assert.Greater(t, fileSizeKB, int64(0), "File should have content")
	assert.LessOrEqual(t, fileSizeKB, int64(300), "File should be under 300KB")
}

func TestDownloadAndSaveImageInDirsWithCompression_SlowPath(t *testing.T) {
	setupTestProcess(t)

	// Create a PNG image that needs format conversion (slow path)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/png")
		w.WriteHeader(http.StatusOK)

		// Create a small PNG image
		img := createTestImage(200, 200)
		err := png.Encode(w, img)
		require.NoError(t, err)
	}))
	defer server.Close()

	tmpDir := createTempDir(t)
	savePath := filepath.Join(tmpDir, "slow_path_test.jpg") // Save as JPEG (needs conversion)

	// Test conditions that should NOT trigger fast path:
	// - Format conversion needed (PNG -> JPEG)
	results, err := DownloadAndSaveImageInDirsWithCompression(
		server.URL,
		[]string{savePath},
		false, // compressWebP = false
		false, // compressToSize = false
		300,   // targetSizeKB
		1,     // maxRetries
	)

	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[savePath])

	// Verify the file was saved and converted to JPEG
	fileInfo, err := os.Stat(results[savePath])
	require.NoError(t, err)
	fileSizeKB := fileInfo.Size() / 1024

	t.Logf("Slow path test - File size: %dKB", fileSizeKB)
	assert.Greater(t, fileSizeKB, int64(0), "File should have content")
}

// TestSaveImage_WebPFallbackExtension tests that when WebP saving fails and falls back to JPEG,
// the file extension is correctly changed from .webp to .jpg
func TestSaveImage_WebPFallbackExtension(t *testing.T) {
	setupTestProcess(t)

	// Create a test image
	img := createTestImage(100, 100)
	tempDir := createTempDir(t)

	// Test with .webp extension
	webpPath := filepath.Join(tempDir, "test.webp")

	// Save with WebP compression (may fallback to JPEG depending on system)
	savedPath, err := SaveImage(img, webpPath, true)
	assert.NoError(t, err)
	assert.NotEmpty(t, savedPath)

	// Check that the file exists
	assert.FileExists(t, savedPath)

	// If WebP encoding failed and fell back to JPEG, the extension should be .jpg
	if filepath.Ext(savedPath) == ".jpg" {
		// Verify the original .webp file doesn't exist
		assert.NoFileExists(t, webpPath)

		// Verify the .jpg file exists and has correct extension
		assert.True(t, strings.HasSuffix(savedPath, ".jpg"))

		// Verify the base name is correct (should be "test.jpg")
		expectedJpgPath := filepath.Join(tempDir, "test.jpg")
		assert.Equal(t, expectedJpgPath, savedPath)

		t.Log("WebP fallback correctly changed extension from .webp to .jpg")
	} else {
		// WebP encoding succeeded, file should have .webp extension
		assert.True(t, strings.HasSuffix(savedPath, ".webp"))
		assert.Equal(t, webpPath, savedPath)

		t.Log("WebP encoding succeeded, extension remained .webp")
	}
}

// TestSaveImage_ForceWebPFallback tests the extension change when WebP fails
func TestSaveImage_ForceWebPFallback(t *testing.T) {
	setupTestProcess(t)

	// Create a test image
	img := createTestImage(100, 100)
	tempDir := createTempDir(t)

	// Create a directory that will cause WebP save to fail
	webpPath := filepath.Join(tempDir, "readonly", "test.webp")

	// Create the directory structure
	dir := filepath.Dir(webpPath)
	err := os.MkdirAll(dir, 0755)
	assert.NoError(t, err)

	// Make directory read-only to force WebP save failure
	err = os.Chmod(dir, 0444)
	assert.NoError(t, err)

	// Restore permissions after test
	defer func() {
		if err := os.Chmod(dir, 0755); err != nil {
			t.Logf("Failed to restore directory permissions: %v", err)
		}
		if err := os.RemoveAll(tempDir); err != nil {
			t.Logf("Failed to remove temp directory: %v", err)
		}
	}()

	// Try to save with WebP compression - should fail and fallback to JPEG
	savedPath, err := SaveImage(img, webpPath, true)

	// The save should fail because we can't write to read-only directory
	// This tests that our error handling works correctly
	assert.Error(t, err)
	assert.Empty(t, savedPath)
}

// TestSaveImage_ExtensionChange tests extension change logic directly
func TestSaveImage_ExtensionChange(t *testing.T) {
	setupTestProcess(t)

	// Test the extension change logic by examining the path transformation
	testCases := []struct {
		name         string
		originalPath string
		expectedJpg  string
	}{
		{
			name:         "webp to jpg",
			originalPath: "/path/to/image.webp",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "WEBP to jpg",
			originalPath: "/path/to/image.WEBP",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "no extension",
			originalPath: "/path/to/image",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "multiple dots",
			originalPath: "/path/to/image.test.webp",
			expectedJpg:  "/path/to/image.test.jpg",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Simulate the extension change logic from SaveImage
			ext := filepath.Ext(tc.originalPath)
			jpgPath := tc.originalPath[:len(tc.originalPath)-len(ext)] + ".jpg"
			assert.Equal(t, tc.expectedJpg, jpgPath)
		})
	}
}

func TestDownloadAndSaveImageInDirs_WithEmptyPaths(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	results, err := DownloadAndSaveImageInDirs(server.URL, []string{}, true)
	assert.Error(t, err)
	assert.Empty(t, results)
}

func TestDownloadAndResizeImage_WithInvalidDimensions(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	// Test with negative dimensions
	img, err := DownloadAndResizeImage(server.URL, -1, -1)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with zero dimensions
	img, err = DownloadAndResizeImage(server.URL, 0, 0)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with one zero dimension
	img, err = DownloadAndResizeImage(server.URL, 100, 0)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with one negative dimension
	img, err = DownloadAndResizeImage(server.URL, 100, -1)
	assert.Error(t, err)
	assert.Nil(t, img)
}

func TestDownloadAndSaveFile(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Successful PDF download",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				// Write a minimal valid PDF file
				if _, err := w.Write([]byte("%PDF-1.4\n%\xe2\xe3\xcf\xd3\n1 0 obj\n<<>>\nendobj\ntrailer\n<<>>\n%%EOF")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.pdf")},
				IsPhoto:   false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					// Read first few bytes to verify PDF header
					data, err := os.ReadFile(path)
					require.NoError(t, err)
					assert.True(t, bytes.HasPrefix(data, []byte("%PDF-")))
				}
			},
		},
		{
			name: "Successful photo download with WebP compression",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.jpg",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					file, err := os.Open(path)
					require.NoError(t, err)
					defer func() {
						if err := file.Close(); err != nil {
							t.Fatalf("Failed to close file: %v", err)
						}
					}()
					header := make([]byte, 12)
					_, err = io.ReadFull(file, header)
					require.NoError(t, err)
					assert.Equal(t, "RIFF", string(header[0:4]))
					assert.Equal(t, "WEBP", string(header[8:12]))
				}
			},
		},
		{
			name: "Multiple save paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL: "http://example.com/test.pdf",
				SavePaths: []string{
					filepath.Join(createTempDir(t), "test1.pdf"),
					filepath.Join(createTempDir(t), "test2.pdf"),
				},
				IsPhoto: false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/error.pdf",
				SavePaths: []string{filepath.Join(createTempDir(t), "error.pdf")},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid save path",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{"/invalid/path/test.pdf"},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Timeout error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(2 * time.Second)
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/timeout.pdf",
				SavePaths:  []string{filepath.Join(createTempDir(t), "timeout.pdf")},
				IsPhoto:    false,
				MaxRetries: 1,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Nil options",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			},
			opts:    nil,
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Empty save paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL

				// Set a shorter timeout for timeout test
				if tt.name == "Timeout error" {
					client := &http.Client{
						Timeout: 1 * time.Second,
						Transport: &http.Transport{
							ResponseHeaderTimeout: 1 * time.Second,
							ExpectContinueTimeout: 1 * time.Second,
							IdleConnTimeout:       1 * time.Second,
							TLSHandshakeTimeout:   1 * time.Second,
							DialContext: (&net.Dialer{
								Timeout: 1 * time.Second,
							}).DialContext,
							DisableKeepAlives:   true,
							MaxIdleConns:        1,
							MaxConnsPerHost:     1,
							MaxIdleConnsPerHost: 1,
							DisableCompression:  true,
							ForceAttemptHTTP2:   false,
						},
					}
					// Use our test client override mechanism
					SetTestHTTPClient(client)
					defer func() {
						SetTestHTTPClient(nil) // Reset to default
					}()
				}
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestSaveNotPic(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		data    []byte
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Valid file and path",
			data:    []byte("test data"),
			path:    filepath.Join(createTempDir(t), "test.txt"),
			wantErr: false,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Invalid path",
			data:    []byte("test data"),
			path:    "/invalid/path/test.txt",
			wantErr: true,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Read-only directory",
			data:    []byte("test data"),
			path:    filepath.Join(createTempDir(t), "test", "test.txt"),
			wantErr: true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			path, err := saveNotPic(tt.data, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)
			data, err := os.ReadFile(path)
			require.NoError(t, err)
			assert.Equal(t, tt.data, data)
		})
	}
}

func TestDownloadAndSaveFile_AdditionalCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Invalid image data with WebP compression",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.jpg",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Server timeout",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(2 * time.Second)
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/timeout.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 1,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Server error with retry",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/error.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 2,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestDownloadAndSaveFile_MoreErrorCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Empty response body",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid image data with JPEG",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid image data with WebP",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/webp")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.webp",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestSaveImage_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		path         string
		compressWebP bool
		wantErr      bool
		setup        func(t *testing.T, path string)
	}{
		{
			name:         "Empty path",
			img:          createTestImage(100, 100),
			path:         "",
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Read-only directory",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "readonly", "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
		{
			name:         "WebP compression with invalid image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "JPEG with invalid image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			path, err := SaveImage(tt.img, tt.path, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)
		})
	}
}

// Test downloadWithRetry with DefaultClient timeout
func TestDownloadWithRetry_DefaultClientTimeout(t *testing.T) {
	setupTestProcess(t)

	// Set test client with timeout
	testClient := &http.Client{
		Timeout: 5 * time.Second,
	}
	SetTestHTTPClient(testClient)
	defer func() {
		SetTestHTTPClient(nil) // Reset to default
	}()

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte("test")); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
	}))

	resp, err := downloadWithRetry(server.URL, 1)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// Test downloadWithRetry with network errors
func TestDownloadWithRetry_NetworkErrors(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		url     string
		wantErr bool
	}{
		{
			name:    "Invalid URL scheme",
			url:     "ftp://invalid-scheme.com",
			wantErr: true,
		},
		{
			name:    "Malformed URL",
			url:     "http://[::1:invalid",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := downloadWithRetry(tt.url, 1)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
			}
		})
	}
}

// Additional tests for better coverage
func TestDownloadAndSaveFile_NetworkErrors(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		url     string
		opts    *DownloadAndSaveFileOptions
		wantErr bool
	}{
		{
			name: "Invalid URL",
			url:  "invalid-url",
			opts: &DownloadAndSaveFileOptions{
				URL:       "invalid-url",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
		},
		{
			name: "Non-existent host",
			url:  "http://non-existent-host-12345.com/test.jpg",
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://non-existent-host-12345.com/test.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 1,
			},
			wantErr: true,
		},
		{
			name: "Empty save paths",
			url:  "http://example.com/test.jpg",
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{},
				IsPhoto:   true,
			},
			wantErr: true, // Should fail because there are no paths to save to
		},
		{
			name:    "Nil options",
			url:     "http://example.com/test.jpg",
			opts:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test for DownloadAndSaveImageInDirs edge cases
func TestDownloadAndSaveImageInDirs_MoreCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		url          string
		savePaths    []string
		compressWebP bool
		wantErr      bool
	}{
		{
			name:         "Empty save paths",
			url:          "http://example.com/test.jpg",
			savePaths:    []string{},
			compressWebP: false,
			wantErr:      true, // Should fail because there are no paths to save to
		},
		{
			name:         "Nil save paths",
			url:          "http://example.com/test.jpg",
			savePaths:    nil,
			compressWebP: false,
			wantErr:      true, // Should fail because there are no paths to save to
		},
		{
			name: "Mixed valid and invalid paths",
			url:  "http://example.com/test.jpg",
			savePaths: []string{
				filepath.Join(createTempDir(t), "valid.jpg"),
				"/invalid/path/test.jpg",
				filepath.Join(createTempDir(t), "valid2.jpg"),
			},
			compressWebP: false,
			wantErr:      false, // Should partially succeed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test server that serves a valid image
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(50, 50)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			}))

			_, err := DownloadAndSaveImageInDirs(server.URL, tt.savePaths, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test WebP fallback to JPEG
func TestDownloadAndSaveFile_WebPFallback(t *testing.T) {
	setupTestProcess(t)

	// Create a test server that serves an image
	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	// Test with WebP compression to an invalid path (should fallback to JPEG)
	tempDir := createTempDir(t)
	webpPath := filepath.Join(tempDir, "test.webp")

	// Make the directory read-only to force WebP save to fail
	if err := os.Chmod(tempDir, 0444); err != nil {
		t.Fatalf("Failed to make directory read-only: %v", err)
	}
	defer func() {
		// Restore permissions for cleanup
		if err := os.Chmod(tempDir, 0755); err != nil {
			t.Logf("Failed to restore directory permissions: %v", err)
		}
	}()

	opts := &DownloadAndSaveFileOptions{
		URL:          server.URL,
		SavePaths:    []string{webpPath},
		IsPhoto:      true,
		CompressWebP: true,
		MaxRetries:   1,
	}

	results, err := DownloadAndSaveFile(opts)
	// Should fail because both WebP and JPEG fallback will fail due to read-only directory
	assert.Error(t, err)
	assert.Empty(t, results)
}

// Test DownloadAndSaveFile with empty data
func TestDownloadAndSaveFile_EmptyData(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/pdf")
		w.WriteHeader(http.StatusOK)
		// Don't write any data
	}))

	opts := &DownloadAndSaveFileOptions{
		URL:       server.URL,
		SavePaths: []string{filepath.Join(createTempDir(t), "empty.pdf")},
		IsPhoto:   false,
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)

	// Verify the file exists but is empty
	data, err := os.ReadFile(results[0])
	assert.NoError(t, err)
	assert.Empty(t, data)
}

// Test DownloadAndSaveFile with MaxRetries = 0 (should use default)
func TestDownloadAndSaveFile_DefaultRetries(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte("test content")); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
	}))

	opts := &DownloadAndSaveFileOptions{
		URL:        server.URL,
		SavePaths:  []string{filepath.Join(createTempDir(t), "test.txt")},
		IsPhoto:    false,
		MaxRetries: 0, // Should use default
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[0])
}

// Test environment variable parsing functions
func TestGetEnvAsInt64(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		envKey       string
		envValue     string
		defaultValue int64
		expected     int64
		setEnv       bool
	}{
		{
			name:         "Valid environment variable",
			envKey:       "TEST_INT64_VALID",
			envValue:     "12345",
			defaultValue: 999,
			expected:     12345,
			setEnv:       true,
		},
		{
			name:         "Invalid environment variable",
			envKey:       "TEST_INT64_INVALID",
			envValue:     "not_a_number",
			defaultValue: 999,
			expected:     999,
			setEnv:       true,
		},
		{
			name:         "Empty environment variable",
			envKey:       "TEST_INT64_EMPTY",
			envValue:     "",
			defaultValue: 999,
			expected:     999,
			setEnv:       true,
		},
		{
			name:         "Unset environment variable",
			envKey:       "TEST_INT64_UNSET",
			envValue:     "",
			defaultValue: 999,
			expected:     999,
			setEnv:       false,
		},
		{
			name:         "Negative number",
			envKey:       "TEST_INT64_NEGATIVE",
			envValue:     "-12345",
			defaultValue: 999,
			expected:     -12345,
			setEnv:       true,
		},
		{
			name:         "Zero value",
			envKey:       "TEST_INT64_ZERO",
			envValue:     "0",
			defaultValue: 999,
			expected:     0,
			setEnv:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up environment variable after test
			defer func() {
				if err := os.Unsetenv(tt.envKey); err != nil {
					t.Logf("Failed to unset environment variable: %v", err)
				}
			}()

			if tt.setEnv {
				if err := os.Setenv(tt.envKey, tt.envValue); err != nil {
					t.Fatalf("Failed to set environment variable: %v", err)
				}
			}

			result := getEnvAsInt64(tt.envKey, tt.defaultValue)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetEnvAsInt(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		envKey       string
		envValue     string
		defaultValue int
		expected     int
		setEnv       bool
	}{
		{
			name:         "Valid environment variable",
			envKey:       "TEST_INT_VALID",
			envValue:     "123",
			defaultValue: 999,
			expected:     123,
			setEnv:       true,
		},
		{
			name:         "Invalid environment variable",
			envKey:       "TEST_INT_INVALID",
			envValue:     "not_a_number",
			defaultValue: 999,
			expected:     999,
			setEnv:       true,
		},
		{
			name:         "Empty environment variable",
			envKey:       "TEST_INT_EMPTY",
			envValue:     "",
			defaultValue: 999,
			expected:     999,
			setEnv:       true,
		},
		{
			name:         "Unset environment variable",
			envKey:       "TEST_INT_UNSET",
			envValue:     "",
			defaultValue: 999,
			expected:     999,
			setEnv:       false,
		},
		{
			name:         "Negative number",
			envKey:       "TEST_INT_NEGATIVE",
			envValue:     "-123",
			defaultValue: 999,
			expected:     -123,
			setEnv:       true,
		},
		{
			name:         "Zero value",
			envKey:       "TEST_INT_ZERO",
			envValue:     "0",
			defaultValue: 999,
			expected:     0,
			setEnv:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up environment variable after test
			defer func() {
				if err := os.Unsetenv(tt.envKey); err != nil {
					t.Logf("Failed to unset environment variable: %v", err)
				}
			}()

			if tt.setEnv {
				if err := os.Setenv(tt.envKey, tt.envValue); err != nil {
					t.Fatalf("Failed to set environment variable: %v", err)
				}
			}

			result := getEnvAsInt(tt.envKey, tt.defaultValue)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test downloadImageData function
func TestDownloadImageData(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		maxRetries     int
		wantErr        bool
		checkData      func(t *testing.T, data []byte)
	}{
		{
			name: "Small image download",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			maxRetries: 1,
			wantErr:    false,
			checkData: func(t *testing.T, data []byte) {
				assert.NotEmpty(t, data)
				assert.True(t, len(data) > 0)
			},
		},
		{
			name: "Large image download (>10MB simulation)",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				// Create a large image
				img := createTestImage(2000, 2000)
				if err := jpeg.Encode(w, img, &jpeg.Options{Quality: 100}); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			maxRetries: 1,
			wantErr:    false,
			checkData: func(t *testing.T, data []byte) {
				assert.NotEmpty(t, data)
				sizeMB := float64(len(data)) / (1024 * 1024)
				t.Logf("Downloaded image size: %.2f MB", sizeMB)
				assert.True(t, sizeMB > 0)
			},
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			maxRetries: 1,
			wantErr:    true,
			checkData: func(t *testing.T, data []byte) {
				assert.Nil(t, data)
			},
		},
		{
			name: "Empty response",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				// Don't write any data
			},
			maxRetries: 1,
			wantErr:    false,
			checkData: func(t *testing.T, data []byte) {
				assert.Empty(t, data)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			data, err := downloadImageData(server.URL, tt.maxRetries)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			tt.checkData(t, data)
		})
	}
}

// Test resizeImageToMaxDimension function with edge cases
func TestResizeImageToMaxDimension_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name        string
		img         image.Image
		isWebP      bool
		wantErr     bool
		checkResult func(t *testing.T, img image.Image, finalSizeKB int)
	}{
		{
			name:        "Nil image",
			img:         nil,
			isWebP:      false,
			wantErr:     true,
			checkResult: func(t *testing.T, img image.Image, finalSizeKB int) {},
		},
		{
			name:    "Large image resize",
			img:     createTestImage(2000, 1500),
			isWebP:  false,
			wantErr: false,
			checkResult: func(t *testing.T, img image.Image, finalSizeKB int) {
				assert.NotNil(t, img)
				assert.Greater(t, finalSizeKB, 0)
				bounds := img.Bounds()
				// Should be resized to max dimension
				maxDim := bounds.Dx()
				if bounds.Dy() > maxDim {
					maxDim = bounds.Dy()
				}
				assert.LessOrEqual(t, maxDim, MAX_DIMENSION_PIXELS)
			},
		},
		{
			name:    "Small image no resize",
			img:     createTestImage(500, 500),
			isWebP:  false,
			wantErr: false,
			checkResult: func(t *testing.T, img image.Image, finalSizeKB int) {
				assert.NotNil(t, img)
				bounds := img.Bounds()
				// Should maintain original size since it's smaller than max dimension
				assert.Equal(t, 500, bounds.Dx())
				assert.Equal(t, 500, bounds.Dy())
			},
		},
		{
			name:    "WebP resize",
			img:     createTestImage(1600, 1200),
			isWebP:  true,
			wantErr: false,
			checkResult: func(t *testing.T, img image.Image, finalSizeKB int) {
				assert.NotNil(t, img)
				assert.GreaterOrEqual(t, finalSizeKB, 0)
				bounds := img.Bounds()
				maxDim := bounds.Dx()
				if bounds.Dy() > maxDim {
					maxDim = bounds.Dy()
				}
				assert.LessOrEqual(t, maxDim, MAX_DIMENSION_PIXELS)
			},
		},
		{
			name:    "Very small image",
			img:     createTestImage(10, 10),
			isWebP:  false,
			wantErr: false,
			checkResult: func(t *testing.T, img image.Image, finalSizeKB int) {
				assert.NotNil(t, img)
				bounds := img.Bounds()
				// Should maintain small size
				assert.Equal(t, 10, bounds.Dx())
				assert.Equal(t, 10, bounds.Dy())
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resizedImg, finalSizeKB, err := ResizeImageToMaxDimension(tt.img, tt.isWebP)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resizedImg)
				assert.Equal(t, 0, finalSizeKB)
			} else {
				assert.NoError(t, err)
				tt.checkResult(t, resizedImg, finalSizeKB)
			}
		})
	}
}

// Test encodeImageToJPEG and encodeImageToWebP functions
func TestEncodeImageFunctions(t *testing.T) {
	setupTestProcess(t)

	img := createTestImage(100, 100)

	// Test JPEG encoding
	jpegData, err := encodeImageToJPEG(img, 85)
	assert.NoError(t, err)
	assert.NotEmpty(t, jpegData)
	assert.True(t, bytes.HasPrefix(jpegData, []byte{0xFF, 0xD8, 0xFF})) // JPEG magic bytes

	// Test JPEG encoding with different quality
	jpegData2, err := encodeImageToJPEG(img, 50)
	assert.NoError(t, err)
	assert.NotEmpty(t, jpegData2)
	// Lower quality should generally result in smaller file
	assert.True(t, len(jpegData2) <= len(jpegData))

	// Test WebP encoding
	webpData, err := encodeImageToWebP(img)
	assert.NoError(t, err)
	assert.NotEmpty(t, webpData)
	assert.True(t, bytes.HasPrefix(webpData, []byte("RIFF"))) // WebP magic bytes

	// Test with different quality levels
	jpegData3, err := encodeImageToJPEG(img, 100)
	assert.NoError(t, err)
	assert.NotEmpty(t, jpegData3)
	// Higher quality should generally result in larger file
	assert.True(t, len(jpegData3) >= len(jpegData))

	// Test with very low quality
	jpegData4, err := encodeImageToJPEG(img, 10)
	assert.NoError(t, err)
	assert.NotEmpty(t, jpegData4)
	// Very low quality should result in smaller file
	assert.True(t, len(jpegData4) <= len(jpegData2))
}

// Test processAndSaveImage function for better coverage
func TestProcessAndSaveImage_Coverage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		savePath     string
		compressWebP bool
		wantErr      bool
		setup        func(t *testing.T, path string)
		checkResult  func(t *testing.T, savedPath string)
	}{
		{
			name:         "Save as JPEG",
			img:          createTestImage(100, 100),
			savePath:     filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				assert.True(t, strings.HasSuffix(savedPath, ".jpg"))
			},
		},
		{
			name:         "Save as WebP",
			img:          createTestImage(100, 100),
			savePath:     filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				// Could be .webp or .jpg (fallback)
				assert.True(t, strings.HasSuffix(savedPath, ".webp") || strings.HasSuffix(savedPath, ".jpg"))
			},
		},
		{
			name:         "WebP fallback to JPEG",
			img:          createTestImage(100, 100),
			savePath:     filepath.Join(createTempDir(t), "readonly", "test.webp"),
			compressWebP: true,
			wantErr:      true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
			checkResult: func(t *testing.T, savedPath string) {
				// Should fail due to read-only directory
				assert.Empty(t, savedPath)
			},
		},
		{
			name:         "Nil image",
			img:          nil,
			savePath:     filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
			checkResult: func(t *testing.T, savedPath string) {
				assert.Empty(t, savedPath)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.savePath)

			savedPath, err := processAndSaveImage(tt.img, tt.savePath, tt.compressWebP)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			tt.checkResult(t, savedPath)
		})
	}
}

// Test DownloadAndSaveImageWithSmartCompression for better coverage
func TestDownloadAndSaveImageWithSmartCompression_Coverage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		targetSizeKB   int
		compressWebP   bool
		wantErr        bool
		checkResult    func(t *testing.T, savedPath string)
	}{
		{
			name: "Valid image compression",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(300, 300)
				if err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95}); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			targetSizeKB: 100,
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				fileInfo, err := os.Stat(savedPath)
				assert.NoError(t, err)
				fileSizeKB := fileInfo.Size() / 1024
				t.Logf("File size: %d KB", fileSizeKB)
			},
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			targetSizeKB: 100,
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, savedPath string) {
				assert.Empty(t, savedPath)
			},
		},
		{
			name: "Invalid image data",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			targetSizeKB: 100,
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, savedPath string) {
				assert.Empty(t, savedPath)
			},
		},
		{
			name: "Zero target size (should use default)",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(200, 200)
				if err := jpeg.Encode(w, img, &jpeg.Options{Quality: 95}); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			targetSizeKB: 0,
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			tempDir := createTempDir(t)
			savePath := filepath.Join(tempDir, "compressed.jpg")

			savedPath, err := DownloadAndSaveImageWithSmartCompression(
				server.URL,
				savePath,
				tt.targetSizeKB,
				tt.compressWebP,
				1, // maxRetries
			)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			tt.checkResult(t, savedPath)
		})
	}
}

// Test readBodyWithRetry function - simplified version
func TestReadBodyWithRetry(t *testing.T) {
	setupTestProcess(t)

	// Test successful read
	t.Run("Successful read", func(t *testing.T) {
		server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			if _, err := w.Write([]byte("test data")); err != nil {
				t.Fatalf("Failed to write data: %v", err)
			}
		}))

		resp, err := http.Get(server.URL)
		assert.NoError(t, err)
		defer func() {
			if err := resp.Body.Close(); err != nil {
				t.Logf("Failed to close response body: %v", err)
			}
		}()

		data, err := readBodyWithRetry(resp, server.URL, 3)
		assert.NoError(t, err)
		assert.Equal(t, []byte("test data"), data)
	})

	// Test with empty response
	t.Run("Empty response", func(t *testing.T) {
		server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			// Don't write any data
		}))

		resp, err := http.Get(server.URL)
		assert.NoError(t, err)
		defer func() {
			if err := resp.Body.Close(); err != nil {
				t.Logf("Failed to close response body: %v", err)
			}
		}()

		data, err := readBodyWithRetry(resp, server.URL, 3)
		assert.NoError(t, err)
		assert.Empty(t, data)
	})
}

// Test new functions added for memory optimization
func TestProcessAndSaveImage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		path         string
		compressWebP bool
		wantErr      bool
		checkResult  func(t *testing.T, savedPath string)
	}{
		{
			name:         "Save as JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				assert.True(t, strings.HasSuffix(savedPath, ".jpg"))
			},
		},
		{
			name:         "Save as WebP",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				// Could be .webp or .jpg depending on WebP support
				assert.True(t, strings.HasSuffix(savedPath, ".webp") || strings.HasSuffix(savedPath, ".jpg"))
			},
		},
		{
			name:         "WebP fallback to JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			checkResult: func(t *testing.T, savedPath string) {
				assert.FileExists(t, savedPath)
				// Should exist regardless of format
			},
		},
		{
			name:         "Nil image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, savedPath string) {
				assert.Empty(t, savedPath)
			},
		},
		{
			name:         "Invalid path",
			img:          createTestImage(100, 100),
			path:         "/invalid/path/test.jpg",
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, savedPath string) {
				assert.Empty(t, savedPath)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			savedPath, err := processAndSaveImage(tt.img, tt.path, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, savedPath)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, savedPath)
			}
			tt.checkResult(t, savedPath)
		})
	}
}

func TestSaveFileStreaming(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name        string
		data        []byte
		path        string
		wantErr     bool
		checkResult func(t *testing.T, savedPath string, originalData []byte)
	}{
		{
			name:    "Valid file streaming",
			data:    []byte("test file content for streaming"),
			path:    filepath.Join(createTempDir(t), "test.txt"),
			wantErr: false,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.FileExists(t, savedPath)
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, originalData, data)
			},
		},
		{
			name:    "Large file streaming",
			data:    bytes.Repeat([]byte("large file content "), 1000),
			path:    filepath.Join(createTempDir(t), "large.txt"),
			wantErr: false,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.FileExists(t, savedPath)
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, originalData, data)
			},
		},
		{
			name:    "Empty file",
			data:    []byte{},
			path:    filepath.Join(createTempDir(t), "empty.txt"),
			wantErr: false,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.FileExists(t, savedPath)
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, originalData, data)
			},
		},
		{
			name:    "Binary data",
			data:    []byte{0x00, 0x01, 0x02, 0xFF, 0xFE, 0xFD},
			path:    filepath.Join(createTempDir(t), "binary.bin"),
			wantErr: false,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.FileExists(t, savedPath)
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, originalData, data)
			},
		},
		{
			name:    "Invalid path",
			data:    []byte("test content"),
			path:    "/invalid/path/test.txt",
			wantErr: true,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.Empty(t, savedPath)
			},
		},
		{
			name:    "Nested directory creation",
			data:    []byte("nested file content"),
			path:    filepath.Join(createTempDir(t), "nested", "deep", "test.txt"),
			wantErr: false,
			checkResult: func(t *testing.T, savedPath string, originalData []byte) {
				assert.FileExists(t, savedPath)
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, originalData, data)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reader := bytes.NewReader(tt.data)
			savedPath, err := saveFileStreaming(reader, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, savedPath)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.path, savedPath)
			}
			tt.checkResult(t, savedPath, tt.data)
		})
	}
}

func TestCheckMemoryAvailable(t *testing.T) {
	setupTestProcess(t)

	// Test the memory check function
	result := checkMemoryAvailable()
	// The function should return a boolean value (true or false)
	// In test environment, it might return false due to high goroutine count
	assert.IsType(t, true, result, "checkMemoryAvailable should return a boolean")

	// Log the result for debugging
	t.Logf("checkMemoryAvailable returned: %v", result)
}

func TestConfigurableMemoryLimits(t *testing.T) {
	// Test default values
	defaultMemory := getEnvAsInt64("GOFILE_MAX_MEMORY_FOR_PROCESSING", defaultMaxMemoryForProcessing)
	defaultGoroutines := getEnvAsInt("GOFILE_MAX_GOROUTINES", defaultMaxGoroutines)

	t.Logf("Default memory limit: %d bytes (%d MB)", defaultMemory, defaultMemory/(1024*1024))
	t.Logf("Default goroutine limit: %d", defaultGoroutines)

	// Test that the global variables are set correctly
	t.Logf("Global maxMemoryForProcessing: %d bytes (%d MB)", maxMemoryForProcessing, maxMemoryForProcessing/(1024*1024))
	t.Logf("Global maxGoroutines: %d", maxGoroutines)

	// Verify they match expected defaults (unless overridden by environment)
	if os.Getenv("GOFILE_MAX_MEMORY_FOR_PROCESSING") == "" {
		assert.Equal(t, int64(defaultMaxMemoryForProcessing), maxMemoryForProcessing)
	}
	if os.Getenv("GOFILE_MAX_GOROUTINES") == "" {
		assert.Equal(t, defaultMaxGoroutines, maxGoroutines)
	}
}

func TestForceGC(t *testing.T) {
	setupTestProcess(t)

	// Test that forceGC doesn't panic without message
	assert.NotPanics(t, func() {
		forceGC()
	})

	// Test that forceGC doesn't panic with message
	assert.NotPanics(t, func() {
		forceGC("test message for GC")
	})

	// Test that forceGC doesn't panic with empty message
	assert.NotPanics(t, func() {
		forceGC("")
	})

	// Test that forceGC doesn't panic with multiple messages (only first is used)
	assert.NotPanics(t, func() {
		forceGC("first message", "second message")
	})
}

func TestSmartForceGC(t *testing.T) {
	setupTestProcess(t)

	// Save original values
	originalBatchSize := gcBatchSize
	originalMinInterval := gcMinInterval
	originalEmergencyMemoryGB := gcEmergencyMemoryGB

	// Set test values
	gcBatchSize = 3 // Small batch size for testing
	gcMinInterval = 1 * time.Second
	gcEmergencyMemoryGB = 1 // 1GB threshold

	defer func() {
		// Restore original values
		gcBatchSize = originalBatchSize
		gcMinInterval = originalMinInterval
		gcEmergencyMemoryGB = originalEmergencyMemoryGB
	}()

	// Test that smartForceGC doesn't panic
	assert.NotPanics(t, func() {
		smartForceGC("test message")
	})

	// Get starting count
	startCount := atomic.LoadInt64(&processedImageCount)

	// Test batch processing trigger
	// First call should increment counter
	smartForceGC("call 1")
	count1 := atomic.LoadInt64(&processedImageCount)
	assert.Equal(t, startCount+1, count1, "First call should increment counter")

	// Second call should increment counter
	smartForceGC("call 2")
	count2 := atomic.LoadInt64(&processedImageCount)
	assert.Equal(t, startCount+2, count2, "Second call should increment counter")

	// Continue until we hit a multiple of batch size to trigger GC
	// We need to find the next multiple of 3
	currentCount := count2
	for currentCount%gcBatchSize != 0 {
		smartForceGC("increment call")
		currentCount = atomic.LoadInt64(&processedImageCount)
	}

	// Verify we triggered GC when hitting the batch size multiple
	assert.True(t, currentCount%gcBatchSize == 0, "Should have triggered GC at batch boundary")
}

func TestLargeImageWarning(t *testing.T) {
	setupTestProcess(t)

	// Create a mock server that returns a large file (simulated with Content-Length header)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simulate a 15MB file
		w.Header().Set("Content-Length", "15728640") // 15MB in bytes
		w.Header().Set("Content-Type", "application/octet-stream")

		// Return a small actual file for testing (non-photo to avoid image processing issues)
		testData := []byte("test file content for large file warning")
		if _, err := w.Write(testData); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
	}))
	defer server.Close()

	// Create temp directory for test
	tempDir := t.TempDir()
	savePath := filepath.Join(tempDir, "large_test.bin")

	// Test downloading with large Content-Length header (non-photo file)
	opts := &DownloadAndSaveFileOptions{
		URL:          server.URL + "/large_file.bin",
		SavePaths:    []string{savePath},
		CompressWebP: false,
		IsPhoto:      false, // Use non-photo to avoid image processing
		MaxRetries:   1,
	}

	// This should trigger the warning for >10MB files when IsPhoto is true
	// Let's test with IsPhoto=true to trigger the warning path
	opts.IsPhoto = true

	// This should trigger the warning for >10MB images but may fail due to invalid image data
	// So let's just test that the function doesn't panic and handles the case
	_, err := DownloadAndSaveFile(opts)

	// We expect an error due to invalid image data, but the warning should still be logged
	// The important thing is that the function doesn't panic
	t.Logf("Download result (expected to fail due to invalid image): %v", err)
}

func TestDownloadAndSaveFile_MemoryOptimizations(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Large image with memory check",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				// Create a larger image to test memory handling
				img := createTestImage(500, 500)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/large.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "large.jpg")},
				IsPhoto:   true,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
		{
			name: "Non-photo file streaming",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				// Write a large PDF-like content
				content := bytes.Repeat([]byte("%PDF-1.4 content "), 1000)
				if _, err := w.Write(content); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/large.pdf",
				SavePaths: []string{filepath.Join(createTempDir(t), "large.pdf")},
				IsPhoto:   false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					// Verify content
					data, err := os.ReadFile(path)
					require.NoError(t, err)
					assert.True(t, bytes.HasPrefix(data, []byte("%PDF-1.4")))
				}
			},
		},
		{
			name: "Multiple non-photo paths with re-download",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "text/plain")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("test content for multiple saves")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL: "http://example.com/test.txt",
				SavePaths: []string{
					filepath.Join(createTempDir(t), "test1.txt"),
					filepath.Join(createTempDir(t), "test2.txt"),
				},
				IsPhoto: false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
					data, err := os.ReadFile(path)
					require.NoError(t, err)
					assert.Equal(t, "test content for multiple saves", string(data))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestDownloadAndSaveFile_ErrorHandling(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
	}{
		{
			name: "Photo with insufficient memory (simulated)",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: false, // Memory check currently always returns true
		},
		{
			name: "Photo decode error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/invalid.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "invalid.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
		},
		{
			name: "Non-photo streaming error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "text/plain")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("test content")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.txt",
				SavePaths: []string{"/invalid/path/test.txt"},
				IsPhoto:   false,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
		})
	}
}

// Test edge cases for better coverage
func TestDownloadAndSaveFile_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Photo with large size warning",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				// Create a large image that will trigger size warning
				img := createTestImage(1000, 1000)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/large.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "large.jpg")},
				IsPhoto:   true,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				assert.FileExists(t, results[0])
			},
		},
		{
			name: "Non-photo with single path",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "text/plain")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("single path content")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/single.txt",
				SavePaths: []string{filepath.Join(createTempDir(t), "single.txt")},
				IsPhoto:   false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				assert.FileExists(t, results[0])
				data, err := os.ReadFile(results[0])
				require.NoError(t, err)
				assert.Equal(t, "single path content", string(data))
			},
		},
		{
			name: "Photo with WebP compression and multiple paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(200, 200)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL: "http://example.com/multi.jpg",
				SavePaths: []string{
					filepath.Join(createTempDir(t), "multi1.webp"),
					filepath.Join(createTempDir(t), "multi2.webp"),
				},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			if tt.checkResult != nil {
				tt.checkResult(t, results)
			}
		})
	}
}

// Additional tests to improve coverage for specific functions
func TestDownloadAndSaveImageInDirs_LowCoverage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		url            string
		savePaths      []string
		compressWebP   bool
		wantErr        bool
		checkResult    func(t *testing.T, results map[string]string)
	}{
		{
			name: "Download failure",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			url:          "http://example.com/error.jpg",
			savePaths:    []string{filepath.Join(createTempDir(t), "error.jpg")},
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid image format",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("not an image")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			url:          "http://example.com/invalid.jpg",
			savePaths:    []string{filepath.Join(createTempDir(t), "invalid.jpg")},
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Partial save failure",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			url: "http://example.com/partial.jpg",
			savePaths: []string{
				filepath.Join(createTempDir(t), "valid.jpg"),
				"/invalid/readonly/path.jpg",
				filepath.Join(createTempDir(t), "valid2.jpg"),
			},
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				// Should have 2 successful saves out of 3
				assert.Len(t, results, 2)
			},
		},
		{
			name: "WebP compression with save error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			url: "http://example.com/webp.jpg",
			savePaths: []string{
				filepath.Join(createTempDir(t), "good.webp"),
				"/invalid/readonly/bad.webp",
			},
			compressWebP: true,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				// Should have 1 successful save out of 2
				assert.Len(t, results, 1)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			results, err := DownloadAndSaveImageInDirs(server.URL, tt.savePaths, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestSaveFileStreaming_ErrorCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		data    []byte
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Directory creation failure",
			data:    []byte("test content"),
			path:    "/proc/test.txt", // /proc is typically read-only
			wantErr: true,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "File creation failure",
			data:    []byte("test content"),
			path:    filepath.Join(createTempDir(t), "readonly", "test.txt"),
			wantErr: true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
		{
			name:    "Empty path",
			data:    []byte("test content"),
			path:    "",
			wantErr: true,
			setup:   func(t *testing.T, path string) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			reader := bytes.NewReader(tt.data)
			savedPath, err := saveFileStreaming(reader, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, savedPath)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.path, savedPath)
			}
		})
	}
}

func TestDownloadWithRetry_MoreCoverage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(attempts *int) (int, []byte, bool) // returns status, data, shouldClose
		maxRetries     int
		wantErr        bool
		expectedTries  int
	}{
		{
			name: "Connection close after response",
			serverBehavior: func(attempts *int) (int, []byte, bool) {
				*attempts++
				if *attempts == 1 {
					return http.StatusOK, []byte("success"), true // Close connection
				}
				return http.StatusOK, []byte("success"), false
			},
			maxRetries:    2,
			wantErr:       false,
			expectedTries: 1,
		},
		{
			name: "Multiple failures then success",
			serverBehavior: func(attempts *int) (int, []byte, bool) {
				*attempts++
				if *attempts <= 2 {
					return http.StatusBadGateway, []byte("error"), false
				}
				return http.StatusOK, []byte("success"), false
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 3,
		},
		{
			name: "Default retries (no parameter)",
			serverBehavior: func(attempts *int) (int, []byte, bool) {
				*attempts++
				return http.StatusOK, []byte("success"), false
			},
			maxRetries:    -1, // Use default
			wantErr:       false,
			expectedTries: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attempts := 0
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				status, data, shouldClose := tt.serverBehavior(&attempts)
				if shouldClose {
					w.Header().Set("Connection", "close")
				}
				w.WriteHeader(status)
				if _, err := w.Write(data); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			}))

			var resp *http.Response
			var err error
			if tt.maxRetries == -1 {
				resp, err = downloadWithRetry(server.URL) // Use default retries
			} else {
				resp, err = downloadWithRetry(server.URL, tt.maxRetries)
			}

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				if resp != nil {
					assert.Equal(t, http.StatusOK, resp.StatusCode)
					if resp.Body != nil {
						if err := resp.Body.Close(); err != nil {
							t.Logf("Failed to close response body: %v", err)
						}
					}
				}
			}
			assert.Equal(t, tt.expectedTries, attempts)
		})
	}
}

// Test more edge cases for DownloadAndResizeImage
func TestDownloadAndResizeImage_MoreCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		width          int
		height         int
		wantErr        bool
		checkSize      func(t *testing.T, img image.Image)
	}{
		{
			name: "Square image resize",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   50,
			height:  50,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 50, bounds.Dx())
				assert.Equal(t, 50, bounds.Dy())
			},
		},
		{
			name: "Very small target size",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(1000, 500)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   1,
			height:  1,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				// Should maintain aspect ratio, so width should be 1, height should be calculated
				assert.Equal(t, 1, bounds.Dx())
				assert.True(t, bounds.Dy() >= 1) // Height should be at least 1
			},
		},
		{
			name: "Extreme aspect ratio",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(1000, 10)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 100, bounds.Dx())
				assert.True(t, bounds.Dy() >= 1) // Height should be very small but at least 1
			},
		},
		{
			name: "Connection timeout",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(15 * time.Second) // Longer than client timeout
				w.WriteHeader(http.StatusOK)
			},
			width:   100,
			height:  100,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			img, err := DownloadAndResizeImage(server.URL, tt.width, tt.height)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, img)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, img)
			if tt.checkSize != nil {
				tt.checkSize(t, img)
			}
		})
	}
}

// Test saveNotPic with more edge cases
func TestGetImageExtensionFromURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "Simple JPG URL",
			url:      "https://example.com/image.jpg",
			expected: ".jpg",
		},
		{
			name:     "Simple PNG URL",
			url:      "https://example.com/image.png",
			expected: ".png",
		},
		{
			name:     "Complex URL with query params",
			url:      "https://trreb-image.ampre.ca/TFwpXmTUpQsQibxI4kcOudiKycs161X2k-eeF0a1Wjc/rs:fit:1920:1920/aq:size:512000:25:75/wm:.5:so:0:50:.4/wmsh:10/wmt:PHNwYW4gZm9yZWdyb3VuZD0nd2hpdGUnIGZvbnQ9JzY4Jz5TSEFISUQgS0hBV0FKQSBSRUFMIEVTVEFURSBJTkMuLCBCcm9rZXJhZ2U8L3NwYW4-/L3RycmViL2xpc3RpbmdzLzQxLzQ1LzAyLzU0L3AvMDJhOGVmMTYtYTgxMy00OGJiLTllMTktYjA0MWNmYWEyMzViLnBuZw.jpg",
			expected: ".jpg",
		},
		{
			name:     "URL with PNG in path but JPG extension",
			url:      "https://example.com/path/image.png.jpg",
			expected: ".jpg",
		},
		{
			name:     "URL with no extension",
			url:      "https://example.com/image",
			expected: "",
		},
		{
			name:     "URL with PNG pattern in middle",
			url:      "https://example.com/png/image.jpg",
			expected: ".jpg",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getImageExtensionFromURL(tt.url)
			assert.Equal(t, tt.expected, result, "URL: %s", tt.url)
		})
	}
}

func TestNeedsFormatConversion(t *testing.T) {
	tests := []struct {
		name     string
		urlExt   string
		expected bool
	}{
		{
			name:     "JPG doesn't need conversion",
			urlExt:   ".jpg",
			expected: false,
		},
		{
			name:     "JPEG doesn't need conversion",
			urlExt:   ".jpeg",
			expected: false,
		},
		{
			name:     "PNG needs conversion",
			urlExt:   ".png",
			expected: true,
		},
		{
			name:     "WebP needs conversion",
			urlExt:   ".webp",
			expected: true,
		},
		{
			name:     "Unknown extension needs conversion",
			urlExt:   "",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := needsFormatConversion(tt.urlExt)
			assert.Equal(t, tt.expected, result, "Extension: %s", tt.urlExt)
		})
	}
}

func TestPNGToJPGConversion(t *testing.T) {
	// Test if PNG input can be properly saved as JPG
	tests := []struct {
		name         string
		compressWebP bool
		expectFormat string
	}{
		{
			name:         "PNG to JPG with compressWebP=false (should convert)",
			compressWebP: false,
			expectFormat: "JPEG", // Should be converted to JPEG
		},
		{
			name:         "PNG to WebP with compressWebP=true",
			compressWebP: true,
			expectFormat: "WEBP",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a PNG image
			img := createTestImage(100, 100)

			// Create test server that serves PNG data
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/png")
				w.WriteHeader(http.StatusOK)
				if err := png.Encode(w, img); err != nil {
					t.Fatalf("Failed to encode PNG: %v", err)
				}
			}))

			// Create temp directory and file path
			tempDir := createTempDir(t)
			var savePath string
			if tt.compressWebP {
				savePath = filepath.Join(tempDir, "test.webp")
			} else {
				savePath = filepath.Join(tempDir, "test.jpg")
			}

			// Download and save
			results, err := DownloadAndSaveImageInDirs(server.URL, []string{savePath}, tt.compressWebP)
			assert.NoError(t, err)
			assert.Len(t, results, 1)

			// Check that file exists
			savedPath := results[savePath]
			assert.FileExists(t, savedPath)

			// Read the saved file and check its format
			savedData, err := os.ReadFile(savedPath)
			assert.NoError(t, err)
			assert.NotEmpty(t, savedData)

			// Check file format by reading the header
			reader := bytes.NewReader(savedData)
			_, format, err := image.DecodeConfig(reader)
			assert.NoError(t, err)

			t.Logf("Expected format: %s, Actual format: %s", tt.expectFormat, strings.ToUpper(format))
			assert.Equal(t, tt.expectFormat, strings.ToUpper(format), "File format should match expected")
		})
	}
}

func TestProcessImageDataWithResize_OptionalParams(t *testing.T) {
	// Create a small test image
	img := createTestImage(50, 50)

	// Encode to JPEG bytes
	var buf bytes.Buffer
	err := jpeg.Encode(&buf, img, nil)
	require.NoError(t, err)
	imageData := buf.Bytes()

	t.Logf("Original image size: %d bytes", len(imageData))

	// Test 1: Call without targetSizeKB parameter (should use default)
	t.Run("Default targetSizeKB", func(t *testing.T) {
		processedData, err := ProcessImageDataWithResize(imageData)
		assert.NoError(t, err)
		assert.NotEmpty(t, processedData)
		t.Logf("Processed size: %d bytes (using default %d KB target)",
			len(processedData), DEFAULT_TARGET_SIZE_KB)
	})

	// Test 2: Call with explicit targetSizeKB parameter
	t.Run("Explicit targetSizeKB=500", func(t *testing.T) {
		processedData, err := ProcessImageDataWithResize(imageData, 500)
		assert.NoError(t, err)
		assert.NotEmpty(t, processedData)
		t.Logf("Processed size: %d bytes (using 500 KB target)", len(processedData))
	})

	// Test 3: Call with targetSizeKB=0 (should use default)
	t.Run("Zero targetSizeKB (should use default)", func(t *testing.T) {
		processedData, err := ProcessImageDataWithResize(imageData, 0)
		assert.NoError(t, err)
		assert.NotEmpty(t, processedData)
		t.Logf("Processed size: %d bytes (using default %d KB target)",
			len(processedData), DEFAULT_TARGET_SIZE_KB)
	})

	// Test 4: Call with negative targetSizeKB (should use default)
	t.Run("Negative targetSizeKB (should use default)", func(t *testing.T) {
		processedData, err := ProcessImageDataWithResize(imageData, -100)
		assert.NoError(t, err)
		assert.NotEmpty(t, processedData)
		t.Logf("Processed size: %d bytes (using default %d KB target)",
			len(processedData), DEFAULT_TARGET_SIZE_KB)
	})
}

func TestSaveNotPic_MoreCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		data    []byte
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Very large data",
			data:    bytes.Repeat([]byte("large data "), 10000),
			path:    filepath.Join(createTempDir(t), "large.txt"),
			wantErr: false,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Binary data with null bytes",
			data:    []byte{0x00, 0x01, 0x02, 0x00, 0xFF, 0x00, 0xFE},
			path:    filepath.Join(createTempDir(t), "binary.bin"),
			wantErr: false,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Path with special characters",
			data:    []byte("special chars test"),
			path:    filepath.Join(createTempDir(t), "file-with_special.chars.txt"),
			wantErr: false,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Existing file overwrite",
			data:    []byte("new content"),
			path:    filepath.Join(createTempDir(t), "existing.txt"),
			wantErr: false,
			setup: func(t *testing.T, path string) {
				// Create existing file
				if err := os.WriteFile(path, []byte("old content"), 0644); err != nil {
					t.Fatalf("Failed to create existing file: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			savedPath, err := saveNotPic(tt.data, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, savedPath)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.path, savedPath)
				assert.FileExists(t, savedPath)

				// Verify content
				data, err := os.ReadFile(savedPath)
				require.NoError(t, err)
				assert.Equal(t, tt.data, data)
			}
		})
	}
}
